"""
User schema module.
This module defines the schema for User model validation.
"""
from marshmallow import fields, validates, ValidationError, validate, Schema
from app.schemas import ma
from app.models.user import User

class UserSchema(ma.SQLAlchemySchema):
    """Schema for User model."""

    class Meta:
        """Meta class for UserSchema."""
        model = User
        load_instance = True

    id = ma.auto_field(dump_only=True)
    firebase_uid = ma.auto_field(dump_only=True)
    email = fields.Email(required=True)
    name = fields.String(allow_none=True)
    role = fields.String(required=True, validate=validate.OneOf(["administrator", "verkoper", "monteur"]))
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    @validates('email')
    def validate_email(self, email):
        """Validate email field."""
        if not email:
            raise ValidationError('Email is required')
        if len(email) > 120:
            raise ValidationError('Email must be less than 120 characters')

class UserCreateSchema(Schema):
    """Schema for user creation."""
    email = fields.Email(required=True)
    password = fields.String(required=True)
    name = fields.String(allow_none=True)
    role = fields.String(required=True, validate=validate.OneOf(["administrator", "verkoper", "monteur"]))

    @validates('email')
    def validate_email(self, email):
        """Validate email field."""
        if not email:
            raise ValidationError('Email is required')
        if len(email) > 120:
            raise ValidationError('Email must be less than 120 characters')

class UserPermissionSchema(ma.Schema):
    """Schema for user permissions."""

    document_type = fields.String(required=True)
    can_view = fields.Boolean()
    can_upload = fields.Boolean()

class UserWithPermissionsSchema(UserSchema):
    """Schema for User model with permissions."""

    permissions = fields.Dict(keys=fields.String(), values=fields.Nested(UserPermissionSchema))

# Initialize schemas
user_schema = UserSchema()
users_schema = UserSchema(many=True)
user_create_schema = UserCreateSchema()
user_with_permissions_schema = UserWithPermissionsSchema()
users_with_permissions_schema = UserWithPermissionsSchema(many=True)
