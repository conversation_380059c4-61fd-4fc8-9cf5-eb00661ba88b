"""
User Permission schema module.
This module defines the schema for UserPermission model validation.
"""
from marshmallow import fields, validates, ValidationError
from app.schemas import ma
from app.models.user_permission import UserPermission
from app.models.document import ALLOWED_DOCUMENT_TYPES

class UserPermissionSchema(ma.SQLAlchemySchema):
    """Schema for UserPermission model."""
    
    class Meta:
        """Meta class for UserPermissionSchema."""
        model = UserPermission
        load_instance = True
    
    id = ma.auto_field(dump_only=True)
    user_id = fields.Integer(required=True)
    document_type = fields.String(required=True)
    can_view = fields.Boolean()
    can_upload = fields.Boolean()
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)
    
    @validates('document_type')
    def validate_document_type(self, document_type):
        """Validate document_type field."""
        if not document_type:
            raise ValidationError('Document type is required')
        if document_type not in ALLOWED_DOCUMENT_TYPES:
            raise ValidationError(f'Invalid document type. Must be one of {ALLOWED_DOCUMENT_TYPES}')

# Initialize schemas
user_permission_schema = UserPermissionSchema()
user_permissions_schema = UserPermissionSchema(many=True)
